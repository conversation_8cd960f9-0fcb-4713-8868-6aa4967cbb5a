package com.appystore.mrecharge.util;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.util.Log;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * ConfigUrl class provides centralized API URL configuration management
 * for the license activation system.
 * 
 * This class follows Android best practices by:
 * - Providing a singleton pattern for consistent configuration access
 * - Using SharedPreferences for persistent storage
 * - Offering URL validation
 * - Supporting both default and custom API URLs
 * - Thread-safe operations
 * 
 * <AUTHOR> MRecharge Team
 * @version 1.0
 */
public class ConfigUrl {
    
    private static final String TAG = "ConfigUrl";
    
    // SharedPreferences key for storing API URL
    private static final String PREF_KEY_API_URL = "api_url";
    
    // Default API URL for license activation
    private static final String DEFAULT_API_URL = 
        "http://*************/AppyStoreMRecharge/mrecharge_simsupport_telecom/License_of_Tel_beautiful_design/admin/api/device_auth.php";
    
    // Singleton instance
    private static ConfigUrl instance;
    private static final Object lock = new Object();
    
    // Context for accessing SharedPreferences
    private Context context;
    
    /**
     * Private constructor to enforce singleton pattern
     * @param context Application context
     */
    private ConfigUrl(Context context) {
        this.context = context.getApplicationContext();
    }
    
    /**
     * Get singleton instance of ConfigUrl
     * @param context Application context
     * @return ConfigUrl instance
     */
    public static ConfigUrl getInstance(Context context) {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new ConfigUrl(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * Get the current API URL from SharedPreferences or return default
     * @return The API URL to use for license activation
     */
    public String getApiUrl() {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        String savedApiUrl = prefs.getString(PREF_KEY_API_URL, null);
        
        // If no saved URL or it's empty, use the default and save it
        if (savedApiUrl == null || savedApiUrl.trim().isEmpty()) {
            Log.d(TAG, "No saved API URL found, using default: " + DEFAULT_API_URL);
            setApiUrl(DEFAULT_API_URL);
            return DEFAULT_API_URL;
        }
        
        Log.d(TAG, "Using saved API URL: " + savedApiUrl);
        return savedApiUrl;
    }
    
    /**
     * Get the default API URL
     * @return The default API URL
     */
    public String getDefaultApiUrl() {
        return DEFAULT_API_URL;
    }
    
    /**
     * Set a new API URL with validation
     * @param newApiUrl The new API URL to set
     * @return true if URL was valid and saved, false otherwise
     */
    public boolean setApiUrl(String newApiUrl) {
        if (newApiUrl == null || newApiUrl.trim().isEmpty()) {
            Log.e(TAG, "Cannot set empty or null API URL");
            return false;
        }
        
        // Validate URL format
        if (!isValidUrl(newApiUrl)) {
            Log.e(TAG, "Invalid API URL format: " + newApiUrl);
            return false;
        }
        
        // Save to SharedPreferences
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(PREF_KEY_API_URL, newApiUrl.trim());
        boolean success = editor.commit();
        
        if (success) {
            Log.d(TAG, "API URL updated successfully: " + newApiUrl);
        } else {
            Log.e(TAG, "Failed to save API URL to SharedPreferences");
        }
        
        return success;
    }
    
    /**
     * Reset API URL to default value
     * @return true if reset was successful, false otherwise
     */
    public boolean resetToDefault() {
        Log.d(TAG, "Resetting API URL to default: " + DEFAULT_API_URL);
        return setApiUrl(DEFAULT_API_URL);
    }
    
    /**
     * Check if the current API URL is the default one
     * @return true if current URL is default, false otherwise
     */
    public boolean isUsingDefaultUrl() {
        String currentUrl = getApiUrl();
        return DEFAULT_API_URL.equals(currentUrl);
    }
    
    /**
     * Validate if a string is a valid URL
     * @param urlString The URL string to validate
     * @return true if valid URL, false otherwise
     */
    private boolean isValidUrl(String urlString) {
        try {
            URL url = new URL(urlString);
            // Additional validation: must be HTTP or HTTPS
            String protocol = url.getProtocol().toLowerCase();
            if (!"http".equals(protocol) && !"https".equals(protocol)) {
                Log.e(TAG, "URL must use HTTP or HTTPS protocol: " + urlString);
                return false;
            }
            return true;
        } catch (MalformedURLException e) {
            Log.e(TAG, "Malformed URL: " + urlString + ", Error: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Get base URL from the API URL (removes the endpoint path)
     * This is useful for NetworkClient configuration
     * @return Base URL without endpoint path
     */
    public String getBaseUrl() {
        String apiUrl = getApiUrl();
        try {
            URL url = new URL(apiUrl);
            String baseUrl = url.getProtocol() + "://" + url.getHost();
            if (url.getPort() != -1) {
                baseUrl += ":" + url.getPort();
            }
            // Add path up to the last directory
            String path = url.getPath();
            if (path != null && !path.isEmpty()) {
                int lastSlash = path.lastIndexOf('/');
                if (lastSlash > 0) {
                    baseUrl += path.substring(0, lastSlash + 1);
                } else {
                    baseUrl += "/";
                }
            } else {
                baseUrl += "/";
            }
            Log.d(TAG, "Extracted base URL: " + baseUrl);
            return baseUrl;
        } catch (MalformedURLException e) {
            Log.e(TAG, "Error extracting base URL from: " + apiUrl, e);
            // Fallback to a reasonable default
            return "http://*************/AppyStoreMRecharge/";
        }
    }
    
    /**
     * Clear all stored configuration (useful for testing or reset)
     */
    public void clearConfiguration() {
        Log.d(TAG, "Clearing all stored configuration");
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = prefs.edit();
        editor.remove(PREF_KEY_API_URL);
        editor.apply();
    }
    
    /**
     * Get configuration info for debugging
     * @return String containing current configuration details
     */
    public String getConfigInfo() {
        StringBuilder info = new StringBuilder();
        info.append("ConfigUrl Configuration:\n");
        info.append("- Current API URL: ").append(getApiUrl()).append("\n");
        info.append("- Default API URL: ").append(getDefaultApiUrl()).append("\n");
        info.append("- Using Default: ").append(isUsingDefaultUrl()).append("\n");
        info.append("- Base URL: ").append(getBaseUrl()).append("\n");
        return info.toString();
    }
}
